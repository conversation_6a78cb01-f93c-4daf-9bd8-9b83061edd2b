# TMS2Clarity

A mobile application for TMS (Transcranial Magnetic Stimulation) therapy management.

## Security Notice

**Important**: This project uses environment variables for configuration. The `.env` file contains sensitive information and should **never** be committed to version control.

## Setup Instructions

1. Copy the `.env.example` file to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and fill in your actual values:
   ```bash
   # Supabase Configuration
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_url_here
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
   
   # Email Service Configuration
   RESEND_API_KEY=your_resend_api_key_here
   ```

3. For production deployments, use EAS secrets instead of environment variables:
   ```bash
   eas secret:create --scope project --name SUPABASE_URL --value "your-supabase-url"
   eas secret:create --scope project --name SUPABASE_ANON_KEY --value "your-supabase-anon-key"
   eas secret:create --scope project --name RESEND_API_KEY --value "your-resend-api-key"
   ```

## Development

To run the application locally:

```bash
npm install
npm start
```

## Building

To build for Android:
```bash
npm run build:android
```

## Security Best Practices

1. Never commit `.env` files to version control
2. Use EAS secrets for production deployments
3. Rotate API keys regularly
4. Limit the scope and permissions of API keys