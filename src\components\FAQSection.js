import React, { useState, useCallback } from 'react';
import { StyleSheet, View, Text, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// Import hooks
import { useResponsiveDimensions } from '../hooks/useResponsiveDimensions';

// Import constants
import Colors from '../constants/Colors';
import Fonts from '../constants/Fonts';
import Layout from '../constants/Layout';
import Spacing from '../constants/Spacing';

/**
 * Reusable FAQ Section Component
 * Used across multiple screens for consistent FAQ display
 */
const FAQSection = ({ 
  faqItems = [],
  headerText = "FAQ'S",
  title = "Frequently Asked Questions",
  titlePrimaryText = "Frequently Asked ",
  titleBlackText = "Questions",
  showHeader = true,
  style = {}
}) => {
  const { isSmallDevice } = useResponsiveDimensions();
  const [expandedFaq, setExpandedFaq] = useState(null);

  const toggleFaq = useCallback((index) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  }, [expandedFaq]);

  return (
    <View style={[styles.faqSection, style]}>
      {showHeader && (
        <View style={styles.faqHeaderContainer}>
          <View style={styles.faqHeaderLine} />
          <Text style={styles.faqHeaderText}>{headerText}</Text>
          <View style={styles.faqHeaderLine} />
        </View>
      )}
      
      <Text style={[styles.faqTitle, isSmallDevice && { fontSize: 28 }]}>
        <Text style={styles.faqTitlePrimary}>{titlePrimaryText}</Text>
        <Text style={styles.faqTitleBlack}>{titleBlackText}</Text>
      </Text>

      {faqItems.map((item, index) => (
        <Pressable
          key={index}
          style={[styles.faqItem, expandedFaq === index && styles.faqItemExpanded]}
          onPress={() => toggleFaq(index)}
        >
          <View style={styles.faqQuestion}>
            <Text style={[styles.faqQuestionText, isSmallDevice && { fontSize: 14 }]}>
              {item.question}
            </Text>
            <Ionicons 
              name={expandedFaq === index ? "chevron-up" : "chevron-down"} 
              size={24} 
              color="#FFB728" 
            />
          </View>
          {expandedFaq === index && (
            <Text style={[styles.faqAnswerText, isSmallDevice && { fontSize: 14 }]}>
              {item.answer}
            </Text>
          )}
        </Pressable>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  faqSection: {
    paddingTop: Layout.spacing.xlarge,
    paddingBottom: Layout.spacing.xlarge,
    paddingHorizontal: Layout.spacing.large,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    marginHorizontal: Layout.spacing.large,
    marginBottom: Spacing.SECTION_TO_SECTION,
    borderRadius: Layout.borderRadius.large,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  faqHeaderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  faqHeaderLine: {
    flex: 1,
    height: 2,
    backgroundColor: "#FFB728",
    marginHorizontal: 25,
  },
  faqHeaderText: {
    fontSize: 18,
    fontWeight: Fonts.weights.bold,
    color: "#FFB728",
    letterSpacing: 2,
    paddingHorizontal: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  faqTitle: {
    fontSize: 28,
    fontWeight: Fonts.weights.bold,
    marginBottom: 30,
    textAlign: "center",
  },
  faqTitlePrimary: {
    color: "#FFB728",
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  faqTitleBlack: {
    color: Colors.white,
  },
  faqItem: {
    borderWidth: 1,
    borderColor: 'rgba(255, 183, 40, 0.3)',
    borderRadius: Layout.borderRadius.medium,
    marginBottom: 15,
    overflow: "hidden",
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  faqItemExpanded: {
    borderColor: "#FFB728",
    borderWidth: 2,
  },
  faqQuestion: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
  },
  faqQuestionText: {
    fontSize: Fonts.sizes.regular,
    fontWeight: Fonts.weights.medium,
    color: Colors.black,
    flex: 1,
  },
  faqAnswerText: {
    fontSize: Fonts.sizes.medium,
    lineHeight: 20,
    color: '#666666',
    padding: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
  },
});

export default FAQSection; 